import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import pyperclip
import subprocess
import threading

class BabelDOCGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("BabelDOC 参数配置工具")
        self.root.geometry("1000x800")

        # 读取配置文件
        self.config = self.load_config()
        
        # 创建主框架
        main_frame = ttk.Frame(root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建滚动画布
        canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
        
        self.create_widgets(scrollable_frame)

    def load_config(self):
        """读取config.txt配置文件"""
        config = {
            'base_url': 'https://api.openai.com/v1',
            'api': '',
            'model': 'gpt-4o-mini'
        }

        try:
            if os.path.exists('config.txt'):
                with open('config.txt', 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and ':' in line:
                            key, value = line.split(':', 1)
                            config[key.strip()] = value.strip()
        except Exception as e:
            print(f"读取配置文件失败: {e}")

        return config

    def create_widgets(self, parent):
        # 1. 文件选择区域
        file_frame = ttk.LabelFrame(parent, text="文件设置", padding="5")
        file_frame.pack(fill=tk.X, pady=5)

        ttk.Label(file_frame, text="输入文件:").grid(row=0, column=0, sticky=tk.W)
        self.file_var = tk.StringVar()
        self.file_entry = ttk.Entry(file_frame, textvariable=self.file_var, width=50)
        self.file_entry.grid(row=0, column=1, padx=5)
        ttk.Button(file_frame, text="选择单个", command=self.browse_file).grid(row=0, column=2)
        ttk.Button(file_frame, text="批量选择", command=self.browse_multiple_files).grid(row=0, column=3, padx=5)

        # 添加文件列表显示区域
        ttk.Label(file_frame, text="已选文件:").grid(row=1, column=0, sticky=tk.NW)
        self.files_listbox = tk.Listbox(file_frame, height=4, width=70)
        self.files_listbox.grid(row=1, column=1, columnspan=2, padx=5, pady=5, sticky=tk.W+tk.E)

        # 文件操作按钮
        file_btn_frame = ttk.Frame(file_frame)
        file_btn_frame.grid(row=1, column=3, padx=5, sticky=tk.N)
        ttk.Button(file_btn_frame, text="清空列表", command=self.clear_files).pack(pady=2)
        ttk.Button(file_btn_frame, text="单独配置", command=self.configure_individual_files).pack(pady=2)

        # 存储选中的文件列表和每个文件的配置
        self.selected_files = []
        self.file_configs = {}  # 存储每个文件的个性化配置
        
        ttk.Label(file_frame, text="页面范围:").grid(row=2, column=0, sticky=tk.W)
        self.pages_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.pages_var, width=30).grid(row=2, column=1, sticky=tk.W, padx=5)
        ttk.Label(file_frame, text="例: 1,2,1-,-3,3-5 (留空翻译所有页面)").grid(row=2, column=2, columnspan=2, sticky=tk.W)
        
        # 2. 基本设置
        basic_frame = ttk.LabelFrame(parent, text="基本设置", padding="5")
        basic_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(basic_frame, text="源语言:").grid(row=0, column=0, sticky=tk.W)
        self.lang_in_var = tk.StringVar(value="en")
        lang_in_combo = ttk.Combobox(basic_frame, textvariable=self.lang_in_var, width=10)
        lang_in_combo['values'] = ("en", "zh", "ja", "ko", "fr", "de", "es", "ru", "ar")
        lang_in_combo.grid(row=0, column=1, padx=5)
        
        ttk.Label(basic_frame, text="目标语言:").grid(row=0, column=2, sticky=tk.W, padx=(20, 0))
        self.lang_out_var = tk.StringVar(value="zh")
        lang_out_combo = ttk.Combobox(basic_frame, textvariable=self.lang_out_var, width=10)
        lang_out_combo['values'] = ("zh", "en", "ja", "ko", "fr", "de", "es", "ru", "ar")
        lang_out_combo.grid(row=0, column=3, padx=5)
        
        ttk.Label(basic_frame, text="输出目录:").grid(row=1, column=0, sticky=tk.W)
        self.output_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.output_var, width=40).grid(row=1, column=1, columnspan=2, padx=5)
        ttk.Button(basic_frame, text="浏览", command=self.browse_output).grid(row=1, column=3)
        
        # 3. OpenAI设置
        openai_frame = ttk.LabelFrame(parent, text="OpenAI 设置", padding="5")
        openai_frame.pack(fill=tk.X, pady=5)
        
        self.openai_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(openai_frame, text="使用 OpenAI", variable=self.openai_var).grid(row=0, column=0, sticky=tk.W)
        
        ttk.Label(openai_frame, text="模型:").grid(row=1, column=0, sticky=tk.W)
        self.openai_model_var = tk.StringVar(value=self.config.get('model', 'gpt-4o-mini'))
        model_combo = ttk.Combobox(openai_frame, textvariable=self.openai_model_var, width=25)
        model_combo['values'] = (
            "gpt-4o-mini", "gpt-4o", "gpt-4-turbo", "gpt-3.5-turbo",
            "deepseek-chat", "deepseek-coder", "glm-4-flash", "glm-4-plus",
            "claude-3-haiku", "claude-3-sonnet", "qwen-turbo", "qwen-plus",
            "moonshot-v1-8k", "yi-large", "baichuan2-turbo"
        )
        model_combo.grid(row=1, column=1, padx=5)

        # 添加提示标签
        ttk.Label(openai_frame, text="(可手动输入其他模型名)", font=("Arial", 8), foreground="gray").grid(row=1, column=2, sticky=tk.W, padx=5)
        
        ttk.Label(openai_frame, text="Base URL:").grid(row=2, column=0, sticky=tk.W)
        self.openai_base_url_var = tk.StringVar(value=self.config.get('base_url', 'https://api.openai.com/v1'))
        base_url_combo = ttk.Combobox(openai_frame, textvariable=self.openai_base_url_var, width=50)
        base_url_combo['values'] = (
            "https://tbai.xin/v1",
            "https://api.openai.com/v1",
            "https://api.deepseek.com/v1",
            "https://open.bigmodel.cn/api/paas/v4",
            "https://dashscope.aliyuncs.com/compatible-mode/v1"
        )
        base_url_combo.grid(row=2, column=1, columnspan=2, padx=5)
        
        ttk.Label(openai_frame, text="API Key:").grid(row=3, column=0, sticky=tk.W)
        self.openai_api_key_var = tk.StringVar(value=self.config.get('api', ''))
        ttk.Entry(openai_frame, textvariable=self.openai_api_key_var, width=50, show="*").grid(row=3, column=1, columnspan=2, padx=5)
        
        # 4. 兼容性选项
        compat_frame = ttk.LabelFrame(parent, text="兼容性选项", padding="5")
        compat_frame.pack(fill=tk.X, pady=5)
        
        self.enhance_compatibility_var = tk.BooleanVar()
        ttk.Checkbutton(compat_frame, text="启用兼容性增强 (推荐)", variable=self.enhance_compatibility_var).grid(row=0, column=0, sticky=tk.W)
        
        self.skip_clean_var = tk.BooleanVar()
        ttk.Checkbutton(compat_frame, text="跳过 PDF 清理", variable=self.skip_clean_var).grid(row=0, column=1, sticky=tk.W, padx=(20, 0))
        
        self.dual_translate_first_var = tk.BooleanVar()
        ttk.Checkbutton(compat_frame, text="翻译页面优先", variable=self.dual_translate_first_var).grid(row=1, column=0, sticky=tk.W)
        
        self.disable_rich_text_var = tk.BooleanVar()
        ttk.Checkbutton(compat_frame, text="禁用富文本翻译", variable=self.disable_rich_text_var).grid(row=1, column=1, sticky=tk.W, padx=(20, 0))
        
        # 5. 处理选项
        process_frame = ttk.LabelFrame(parent, text="处理选项", padding="5")
        process_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(process_frame, text="每部分最大页数:").grid(row=0, column=0, sticky=tk.W)
        self.max_pages_var = tk.StringVar()
        ttk.Entry(process_frame, textvariable=self.max_pages_var, width=10).grid(row=0, column=1, padx=5)
        ttk.Label(process_frame, text="(留空不分割)").grid(row=0, column=2, sticky=tk.W)
        
        self.skip_scanned_detection_var = tk.BooleanVar()
        ttk.Checkbutton(process_frame, text="跳过扫描文档检测", variable=self.skip_scanned_detection_var).grid(row=1, column=0, sticky=tk.W)
        
        self.ocr_workaround_var = tk.BooleanVar()
        ttk.Checkbutton(process_frame, text="OCR 解决方法", variable=self.ocr_workaround_var).grid(row=1, column=1, sticky=tk.W, padx=(20, 0))
        
        self.translate_table_text_var = tk.BooleanVar()
        ttk.Checkbutton(process_frame, text="翻译表格文本 (实验性)", variable=self.translate_table_text_var).grid(row=2, column=0, sticky=tk.W)

        self.split_short_lines_var = tk.BooleanVar()
        ttk.Checkbutton(process_frame, text="强制分割短行", variable=self.split_short_lines_var).grid(row=2, column=1, sticky=tk.W, padx=(20, 0))

        ttk.Label(process_frame, text="短行分割因子:").grid(row=3, column=0, sticky=tk.W)
        self.short_line_split_factor_var = tk.StringVar(value="0.8")
        ttk.Entry(process_frame, textvariable=self.short_line_split_factor_var, width=10).grid(row=3, column=1, padx=5)
        ttk.Label(process_frame, text="(默认: 0.8)").grid(row=3, column=2, sticky=tk.W)
        
        # 6. 输出控制
        output_frame = ttk.LabelFrame(parent, text="输出控制", padding="5")
        output_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(output_frame, text="水印模式:").grid(row=0, column=0, sticky=tk.W)
        self.watermark_var = tk.StringVar(value="watermarked")
        watermark_combo = ttk.Combobox(output_frame, textvariable=self.watermark_var, width=15)
        watermark_combo['values'] = ("watermarked", "no_watermark", "both")
        watermark_combo.grid(row=0, column=1, padx=5)
        
        self.no_dual_var = tk.BooleanVar()
        ttk.Checkbutton(output_frame, text="不输出双语PDF", variable=self.no_dual_var).grid(row=0, column=2, sticky=tk.W, padx=(20, 0))
        
        self.no_mono_var = tk.BooleanVar()
        ttk.Checkbutton(output_frame, text="不输出单语PDF", variable=self.no_mono_var).grid(row=1, column=0, sticky=tk.W)
        
        # 7. 高级选项
        advanced_frame = ttk.LabelFrame(parent, text="高级选项", padding="5")
        advanced_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(advanced_frame, text="QPS限制:").grid(row=0, column=0, sticky=tk.W)
        self.qps_var = tk.StringVar(value="4")
        ttk.Entry(advanced_frame, textvariable=self.qps_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(advanced_frame, text="最小文本长度:").grid(row=0, column=2, sticky=tk.W, padx=(20, 0))
        self.min_text_length_var = tk.StringVar(value="5")
        ttk.Entry(advanced_frame, textvariable=self.min_text_length_var, width=10).grid(row=0, column=3, padx=5)
        
        self.ignore_cache_var = tk.BooleanVar()
        ttk.Checkbutton(advanced_frame, text="忽略缓存", variable=self.ignore_cache_var).grid(row=1, column=0, sticky=tk.W)
        
        self.debug_var = tk.BooleanVar()
        ttk.Checkbutton(advanced_frame, text="调试模式", variable=self.debug_var).grid(row=1, column=1, sticky=tk.W, padx=(20, 0))
        
        # 8. 自定义系统提示
        prompt_frame = ttk.LabelFrame(parent, text="自定义系统提示", padding="5")
        prompt_frame.pack(fill=tk.X, pady=5)
        
        self.custom_prompt_var = tk.StringVar()
        ttk.Entry(prompt_frame, textvariable=self.custom_prompt_var, width=80).pack(fill=tk.X)
        
        # 9. 命令行预览和操作
        cmd_frame = ttk.LabelFrame(parent, text="生成的命令行", padding="5")
        cmd_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 按钮框架
        btn_frame = ttk.Frame(cmd_frame)
        btn_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(btn_frame, text="生成命令", command=self.generate_command).pack(side=tk.LEFT)
        ttk.Button(btn_frame, text="复制命令", command=self.copy_command).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="批量执行", command=self.batch_execute).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="清空设置", command=self.clear_settings).pack(side=tk.LEFT, padx=5)
        
        # 命令显示区域
        self.cmd_text = tk.Text(cmd_frame, height=8, wrap=tk.WORD, font=("Consolas", 10))
        cmd_scrollbar = ttk.Scrollbar(cmd_frame, orient="vertical", command=self.cmd_text.yview)
        self.cmd_text.configure(yscrollcommand=cmd_scrollbar.set)
        
        self.cmd_text.pack(side="left", fill="both", expand=True)
        cmd_scrollbar.pack(side="right", fill="y")
        
        # 绑定变量更新事件
        self.bind_update_events()
        
        # 初始生成命令
        self.generate_command()
    
    def bind_update_events(self):
        """绑定变量更新事件以实时生成命令"""
        variables = [
            self.file_var, self.pages_var, self.lang_in_var, self.lang_out_var,
            self.output_var, self.openai_model_var, self.openai_base_url_var,
            self.openai_api_key_var, self.max_pages_var, self.watermark_var,
            self.qps_var, self.min_text_length_var, self.custom_prompt_var,
            self.short_line_split_factor_var
        ]
        
        for var in variables:
            var.trace_add("write", lambda *args: self.generate_command())
        
        bool_vars = [
            self.openai_var, self.enhance_compatibility_var, self.skip_clean_var,
            self.dual_translate_first_var, self.disable_rich_text_var,
            self.skip_scanned_detection_var, self.ocr_workaround_var,
            self.translate_table_text_var, self.no_dual_var, self.no_mono_var,
            self.ignore_cache_var, self.debug_var, self.split_short_lines_var
        ]
        
        for var in bool_vars:
            var.trace_add("write", lambda *args: self.generate_command())
    
    def browse_file(self):
        filename = filedialog.askopenfilename(
            title="选择PDF文件",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        if filename:
            self.file_var.set(filename)
            # 清空之前的文件列表，添加单个文件
            self.selected_files = [filename]
            self.update_files_display()

    def browse_multiple_files(self):
        filenames = filedialog.askopenfilenames(
            title="批量选择PDF文件",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        if filenames:
            # 添加到现有文件列表中（去重）
            for filename in filenames:
                if filename not in self.selected_files:
                    self.selected_files.append(filename)
            self.update_files_display()
            # 更新单文件输入框显示第一个文件
            if self.selected_files:
                self.file_var.set(self.selected_files[0])

    def clear_files(self):
        """清空文件列表"""
        self.selected_files = []
        self.file_configs = {}
        self.file_var.set("")
        self.update_files_display()

    def update_files_display(self):
        """更新文件列表显示"""
        self.files_listbox.delete(0, tk.END)
        for i, filepath in enumerate(self.selected_files):
            filename = os.path.basename(filepath)
            # 显示是否有个性化配置
            config_indicator = " [已配置]" if filepath in self.file_configs else ""
            self.files_listbox.insert(tk.END, f"{i+1}. {filename}{config_indicator}")

        # 触发命令更新
        self.generate_command()

    def configure_individual_files(self):
        """为每个文件单独配置参数"""
        if not self.selected_files:
            messagebox.showwarning("警告", "请先选择要配置的文件！")
            return

        # 创建配置窗口
        config_window = tk.Toplevel(self.root)
        config_window.title("文件个性化配置")
        config_window.geometry("800x600")
        config_window.transient(self.root)
        config_window.grab_set()

        # 创建主框架
        main_frame = ttk.Frame(config_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 文件选择区域
        file_select_frame = ttk.LabelFrame(main_frame, text="选择要配置的文件", padding="5")
        file_select_frame.pack(fill=tk.X, pady=5)

        self.config_file_var = tk.StringVar()
        file_combo = ttk.Combobox(file_select_frame, textvariable=self.config_file_var, width=80)
        file_combo['values'] = [os.path.basename(f) for f in self.selected_files]
        file_combo.pack(fill=tk.X, pady=5)
        file_combo.bind('<<ComboboxSelected>>', self.load_file_config)

        # 配置选项区域
        config_options_frame = ttk.LabelFrame(main_frame, text="个性化配置选项", padding="5")
        config_options_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # 创建滚动区域
        canvas = tk.Canvas(config_options_frame)
        scrollbar = ttk.Scrollbar(config_options_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 存储配置变量
        self.individual_config_vars = {}

        self.create_individual_config_options(scrollable_frame)

        # 按钮区域
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X, pady=5)

        ttk.Button(btn_frame, text="保存配置", command=lambda: self.save_file_config(config_window)).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="重置为全局", command=self.reset_to_global).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="关闭", command=config_window.destroy).pack(side=tk.RIGHT, padx=5)

        # 默认选择第一个文件
        if self.selected_files:
            file_combo.set(os.path.basename(self.selected_files[0]))
            self.load_file_config(None)

    def create_individual_config_options(self, parent):
        """创建个性化配置选项"""
        # 页面范围
        pages_frame = ttk.LabelFrame(parent, text="页面设置", padding="5")
        pages_frame.pack(fill=tk.X, pady=5)

        ttk.Label(pages_frame, text="页面范围:").grid(row=0, column=0, sticky=tk.W)
        self.individual_config_vars['pages'] = tk.StringVar()
        ttk.Entry(pages_frame, textvariable=self.individual_config_vars['pages'], width=30).grid(row=0, column=1, padx=5)

        # 兼容性选项
        compat_frame = ttk.LabelFrame(parent, text="兼容性选项", padding="5")
        compat_frame.pack(fill=tk.X, pady=5)

        self.individual_config_vars['enhance_compatibility'] = tk.BooleanVar()
        ttk.Checkbutton(compat_frame, text="启用兼容性增强",
                       variable=self.individual_config_vars['enhance_compatibility']).grid(row=0, column=0, sticky=tk.W)

        self.individual_config_vars['skip_clean'] = tk.BooleanVar()
        ttk.Checkbutton(compat_frame, text="跳过 PDF 清理",
                       variable=self.individual_config_vars['skip_clean']).grid(row=0, column=1, sticky=tk.W, padx=(20, 0))

        self.individual_config_vars['dual_translate_first'] = tk.BooleanVar()
        ttk.Checkbutton(compat_frame, text="翻译页面优先",
                       variable=self.individual_config_vars['dual_translate_first']).grid(row=1, column=0, sticky=tk.W)

        self.individual_config_vars['disable_rich_text'] = tk.BooleanVar()
        ttk.Checkbutton(compat_frame, text="禁用富文本翻译",
                       variable=self.individual_config_vars['disable_rich_text']).grid(row=1, column=1, sticky=tk.W, padx=(20, 0))

        # 处理选项
        process_frame = ttk.LabelFrame(parent, text="处理选项", padding="5")
        process_frame.pack(fill=tk.X, pady=5)

        self.individual_config_vars['skip_scanned_detection'] = tk.BooleanVar()
        ttk.Checkbutton(process_frame, text="跳过扫描文档检测",
                       variable=self.individual_config_vars['skip_scanned_detection']).grid(row=0, column=0, sticky=tk.W)

        self.individual_config_vars['ocr_workaround'] = tk.BooleanVar()
        ttk.Checkbutton(process_frame, text="OCR 解决方法",
                       variable=self.individual_config_vars['ocr_workaround']).grid(row=0, column=1, sticky=tk.W, padx=(20, 0))

        self.individual_config_vars['translate_table_text'] = tk.BooleanVar()
        ttk.Checkbutton(process_frame, text="翻译表格文本 (实验性)",
                       variable=self.individual_config_vars['translate_table_text']).grid(row=1, column=0, sticky=tk.W)

        self.individual_config_vars['split_short_lines'] = tk.BooleanVar()
        ttk.Checkbutton(process_frame, text="强制分割短行",
                       variable=self.individual_config_vars['split_short_lines']).grid(row=1, column=1, sticky=tk.W, padx=(20, 0))

        # 输出控制
        output_frame = ttk.LabelFrame(parent, text="输出控制", padding="5")
        output_frame.pack(fill=tk.X, pady=5)

        ttk.Label(output_frame, text="水印模式:").grid(row=0, column=0, sticky=tk.W)
        self.individual_config_vars['watermark'] = tk.StringVar(value="watermarked")
        watermark_combo = ttk.Combobox(output_frame, textvariable=self.individual_config_vars['watermark'], width=15)
        watermark_combo['values'] = ("watermarked", "no_watermark", "both")
        watermark_combo.grid(row=0, column=1, padx=5)

        self.individual_config_vars['no_dual'] = tk.BooleanVar()
        ttk.Checkbutton(output_frame, text="不输出双语PDF",
                       variable=self.individual_config_vars['no_dual']).grid(row=0, column=2, sticky=tk.W, padx=(20, 0))

        self.individual_config_vars['no_mono'] = tk.BooleanVar()
        ttk.Checkbutton(output_frame, text="不输出单语PDF",
                       variable=self.individual_config_vars['no_mono']).grid(row=1, column=0, sticky=tk.W)

    def load_file_config(self, event):
        """加载选中文件的配置"""
        selected_filename = self.config_file_var.get()
        if not selected_filename:
            return

        # 找到完整路径
        selected_filepath = None
        for filepath in self.selected_files:
            if os.path.basename(filepath) == selected_filename:
                selected_filepath = filepath
                break

        if not selected_filepath:
            return

        # 加载该文件的配置，如果没有则使用全局配置
        if selected_filepath in self.file_configs:
            config = self.file_configs[selected_filepath]
        else:
            # 使用全局配置作为默认值
            config = self.get_current_global_config()

        # 更新界面
        for key, var in self.individual_config_vars.items():
            if key in config:
                var.set(config[key])

    def get_current_global_config(self):
        """获取当前全局配置"""
        return {
            'pages': self.pages_var.get(),
            'enhance_compatibility': self.enhance_compatibility_var.get(),
            'skip_clean': self.skip_clean_var.get(),
            'dual_translate_first': self.dual_translate_first_var.get(),
            'disable_rich_text': self.disable_rich_text_var.get(),
            'skip_scanned_detection': self.skip_scanned_detection_var.get(),
            'ocr_workaround': self.ocr_workaround_var.get(),
            'translate_table_text': self.translate_table_text_var.get(),
            'split_short_lines': self.split_short_lines_var.get(),
            'watermark': self.watermark_var.get(),
            'no_dual': self.no_dual_var.get(),
            'no_mono': self.no_mono_var.get()
        }

    def save_file_config(self, window):
        """保存当前文件的配置"""
        selected_filename = self.config_file_var.get()
        if not selected_filename:
            messagebox.showwarning("警告", "请选择要配置的文件！")
            return

        # 找到完整路径
        selected_filepath = None
        for filepath in self.selected_files:
            if os.path.basename(filepath) == selected_filename:
                selected_filepath = filepath
                break

        if not selected_filepath:
            return

        # 保存配置
        config = {}
        for key, var in self.individual_config_vars.items():
            config[key] = var.get()

        self.file_configs[selected_filepath] = config

        # 更新文件列表显示
        self.update_files_display()

        messagebox.showinfo("成功", f"已保存 {selected_filename} 的个性化配置！")
        window.destroy()

    def reset_to_global(self):
        """重置为全局配置"""
        selected_filename = self.config_file_var.get()
        if not selected_filename:
            messagebox.showwarning("警告", "请选择要重置的文件！")
            return

        # 找到完整路径
        selected_filepath = None
        for filepath in self.selected_files:
            if os.path.basename(filepath) == selected_filename:
                selected_filepath = filepath
                break

        if selected_filepath and selected_filepath in self.file_configs:
            del self.file_configs[selected_filepath]
            self.update_files_display()
            # 重新加载全局配置
            self.load_file_config(None)
            messagebox.showinfo("成功", f"已将 {selected_filename} 重置为全局配置！")
    
    def browse_output(self):
        dirname = filedialog.askdirectory(title="选择输出目录")
        if dirname:
            self.output_var.set(dirname)
    
    def generate_command(self):
        # 如果有多个文件，默认为每个文件生成单独的命令
        if len(self.selected_files) > 1:
            # 为每个文件生成单独的命令
            self.generate_individual_commands()
        else:
            # 生成统一命令（单个文件或无文件）
            self.generate_unified_command()

    def generate_unified_command(self):
        """生成统一的命令（单个文件或无文件）"""
        cmd_parts = ["babeldoc"]

        # 文件路径 - 只处理单个文件
        if self.selected_files:
            # 单个文件
            cmd_parts.append(f'--files "{self.selected_files[0]}"')
        elif self.file_var.get().strip():
            # 兼容原有的单文件输入
            cmd_parts.append(f'--files "{self.file_var.get()}"')

        # 添加其他参数（使用全局配置）
        self.add_common_parameters(cmd_parts, use_global=True)

        # 格式化并显示命令
        self.display_command(cmd_parts)

    def generate_individual_commands(self):
        """为每个文件生成单独的命令"""
        all_commands = []

        for filepath in self.selected_files:
            cmd_parts = ["babeldoc"]
            cmd_parts.append(f'--files "{filepath}"')

            # 使用该文件的个性化配置或全局配置
            if filepath in self.file_configs:
                self.add_common_parameters(cmd_parts, use_global=False, file_config=self.file_configs[filepath])
            else:
                self.add_common_parameters(cmd_parts, use_global=True)

            filename = os.path.basename(filepath)
            command_str = " ".join(cmd_parts)
            all_commands.append(f"# {filename}\n{command_str}")

        # 显示所有命令
        formatted_cmd = "\n\n".join(all_commands)
        self.cmd_text.delete(1.0, tk.END)
        self.cmd_text.insert(1.0, formatted_cmd)

    def add_common_parameters(self, cmd_parts, use_global=True, file_config=None):
        """添加通用参数"""
        if use_global:
            # 使用全局配置
            config = self.get_current_global_config()
        else:
            # 使用文件特定配置
            config = file_config

        # 页面范围
        pages = config.get('pages', self.pages_var.get()) if not use_global else self.pages_var.get()
        if pages and pages.strip():
            cmd_parts.append(f'--pages "{pages}"')

        # 语言设置（始终使用全局设置）
        if self.lang_in_var.get():
            cmd_parts.append(f"--lang-in {self.lang_in_var.get()}")
        if self.lang_out_var.get():
            cmd_parts.append(f"--lang-out {self.lang_out_var.get()}")

        # 输出目录（始终使用全局设置）
        if self.output_var.get().strip():
            cmd_parts.append(f'--output "{self.output_var.get()}"')

        # OpenAI设置（始终使用全局设置）
        if self.openai_var.get():
            cmd_parts.append("--openai")
            if self.openai_model_var.get():
                cmd_parts.append(f'--openai-model "{self.openai_model_var.get()}"')
            if self.openai_base_url_var.get():
                cmd_parts.append(f'--openai-base-url "{self.openai_base_url_var.get()}"')
            if self.openai_api_key_var.get():
                cmd_parts.append(f'--openai-api-key "{self.openai_api_key_var.get()}"')

        # 兼容性选项（可个性化配置）
        enhance_compatibility = config.get('enhance_compatibility', self.enhance_compatibility_var.get()) if not use_global else self.enhance_compatibility_var.get()
        if enhance_compatibility:
            cmd_parts.append("--enhance-compatibility")
        else:
            skip_clean = config.get('skip_clean', self.skip_clean_var.get()) if not use_global else self.skip_clean_var.get()
            if skip_clean:
                cmd_parts.append("--skip-clean")

            dual_translate_first = config.get('dual_translate_first', self.dual_translate_first_var.get()) if not use_global else self.dual_translate_first_var.get()
            if dual_translate_first:
                cmd_parts.append("--dual-translate-first")

            disable_rich_text = config.get('disable_rich_text', self.disable_rich_text_var.get()) if not use_global else self.disable_rich_text_var.get()
            if disable_rich_text:
                cmd_parts.append("--disable-rich-text-translate")

        # 处理选项（可个性化配置）
        if self.max_pages_var.get().strip():
            cmd_parts.append(f"--max-pages-per-part {self.max_pages_var.get()}")

        skip_scanned_detection = config.get('skip_scanned_detection', self.skip_scanned_detection_var.get()) if not use_global else self.skip_scanned_detection_var.get()
        if skip_scanned_detection:
            cmd_parts.append("--skip-scanned-detection")

        ocr_workaround = config.get('ocr_workaround', self.ocr_workaround_var.get()) if not use_global else self.ocr_workaround_var.get()
        if ocr_workaround:
            cmd_parts.append("--ocr-workaround")

        translate_table_text = config.get('translate_table_text', self.translate_table_text_var.get()) if not use_global else self.translate_table_text_var.get()
        if translate_table_text:
            cmd_parts.append("--translate-table-text")

        split_short_lines = config.get('split_short_lines', self.split_short_lines_var.get()) if not use_global else self.split_short_lines_var.get()
        if split_short_lines:
            cmd_parts.append("--split-short-lines")

        if self.short_line_split_factor_var.get() and self.short_line_split_factor_var.get() != "0.8":
            cmd_parts.append(f"--short-line-split-factor {self.short_line_split_factor_var.get()}")

        # 输出控制（可个性化配置）
        watermark = config.get('watermark', self.watermark_var.get()) if not use_global else self.watermark_var.get()
        if watermark != "watermarked":
            cmd_parts.append(f"--watermark-output-mode {watermark}")

        no_dual = config.get('no_dual', self.no_dual_var.get()) if not use_global else self.no_dual_var.get()
        if no_dual:
            cmd_parts.append("--no-dual")

        no_mono = config.get('no_mono', self.no_mono_var.get()) if not use_global else self.no_mono_var.get()
        if no_mono:
            cmd_parts.append("--no-mono")

        # 高级选项（始终使用全局设置）
        if self.qps_var.get() and self.qps_var.get() != "4":
            cmd_parts.append(f"--qps {self.qps_var.get()}")

        if self.min_text_length_var.get() and self.min_text_length_var.get() != "5":
            cmd_parts.append(f"--min-text-length {self.min_text_length_var.get()}")

        if self.ignore_cache_var.get():
            cmd_parts.append("--ignore-cache")

        if self.debug_var.get():
            cmd_parts.append("--debug")

        # 自定义系统提示（始终使用全局设置）
        if self.custom_prompt_var.get().strip():
            cmd_parts.append(f'--custom-system-prompt "{self.custom_prompt_var.get()}"')

    def display_command(self, cmd_parts):
        """显示命令"""
        # 格式化命令 - 提供单行格式
        single_line_cmd = " ".join(cmd_parts)

        # 更新命令显示
        self.cmd_text.delete(1.0, tk.END)
        self.cmd_text.insert(1.0, single_line_cmd)
    
    def copy_command(self):
        command = self.cmd_text.get(1.0, tk.END).strip()
        if command:
            try:
                pyperclip.copy(command)
                messagebox.showinfo("成功", "命令已复制到剪贴板！")
            except:
                # 如果pyperclip不可用，使用tkinter的剪贴板
                self.root.clipboard_clear()
                self.root.clipboard_append(command)
                messagebox.showinfo("成功", "命令已复制到剪贴板！")
        else:
            messagebox.showwarning("警告", "没有可复制的命令！")

    def batch_execute(self):
        """批量执行命令"""
        command_text = self.cmd_text.get(1.0, tk.END).strip()
        if not command_text:
            messagebox.showwarning("警告", "没有可执行的命令！")
            return

        # 解析命令文本，提取所有命令
        commands = self.parse_commands(command_text)

        if not commands:
            messagebox.showwarning("警告", "没有找到有效的命令！")
            return

        # 显示执行方式选择对话框
        self.show_execution_options(commands)

    def show_execution_options(self, commands):
        """显示执行方式选择对话框"""
        options_window = tk.Toplevel(self.root)
        options_window.title("选择执行方式")
        options_window.geometry("500x500")  # 增加窗口高度
        options_window.transient(self.root)
        options_window.grab_set()

        # 确保窗口居中显示
        options_window.update_idletasks()
        x = (options_window.winfo_screenwidth() // 2) - (500 // 2)
        y = (options_window.winfo_screenheight() // 2) - (500 // 2)
        options_window.geometry(f"500x500+{x}+{y}")

        # 主框架
        main_frame = ttk.Frame(options_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 内容框架（用于选项）
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(content_frame, text=f"找到 {len(commands)} 个翻译任务", font=("Arial", 12, "bold"))
        title_label.pack(pady=(0, 20))

        # 执行方式选择
        self.execution_mode = tk.StringVar(value="progress_monitor")

        # 方式1：进度监控面板
        mode1_frame = ttk.LabelFrame(content_frame, text="推荐方式", padding="10")
        mode1_frame.pack(fill=tk.X, pady=5)

        ttk.Radiobutton(mode1_frame, text="统一进度监控面板", variable=self.execution_mode,
                       value="progress_monitor").pack(anchor=tk.W)
        ttk.Label(mode1_frame, text="• 在一个窗口中显示所有任务进度\n• 可以实时查看每个任务状态\n• 支持暂停/继续/取消操作",
                 font=("Arial", 9), foreground="gray").pack(anchor=tk.W, padx=20)

        # 方式2：批处理脚本
        mode2_frame = ttk.LabelFrame(content_frame, text="批处理方式", padding="10")
        mode2_frame.pack(fill=tk.X, pady=5)

        ttk.Radiobutton(mode2_frame, text="生成批处理脚本", variable=self.execution_mode,
                       value="batch_script").pack(anchor=tk.W)
        ttk.Label(mode2_frame, text="• 生成 .bat 文件，可以随时执行\n• 任务按顺序执行，避免资源冲突\n• 可以保存脚本供以后使用",
                 font=("Arial", 9), foreground="gray").pack(anchor=tk.W, padx=20)

        # 方式3：多终端（原方式）
        mode3_frame = ttk.LabelFrame(content_frame, text="传统方式", padding="10")
        mode3_frame.pack(fill=tk.X, pady=5)

        ttk.Radiobutton(mode3_frame, text="多个终端窗口", variable=self.execution_mode,
                       value="multiple_terminals").pack(anchor=tk.W)
        ttk.Label(mode3_frame, text="• 每个任务在独立终端中执行\n• 可以同时运行多个任务\n• 需要手动管理多个窗口",
                 font=("Arial", 9), foreground="gray").pack(anchor=tk.W, padx=20)

        # 按钮框架（固定在底部）
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X, pady=(20, 0), side=tk.BOTTOM)

        # 创建按钮并确保它们可见
        start_btn = ttk.Button(btn_frame, text="开始执行",
                              command=lambda: self.execute_with_selected_mode(commands, options_window))
        start_btn.pack(side=tk.LEFT, padx=(0, 10))

        cancel_btn = ttk.Button(btn_frame, text="取消", command=options_window.destroy)
        cancel_btn.pack(side=tk.RIGHT)

    def execute_with_selected_mode(self, commands, options_window):
        """根据选择的模式执行命令"""
        mode = self.execution_mode.get()
        options_window.destroy()

        if mode == "progress_monitor":
            self.execute_with_progress_monitor(commands)
        elif mode == "batch_script":
            self.generate_batch_script(commands)
        elif mode == "multiple_terminals":
            self.execute_commands_in_terminals(commands)

    def execute_with_progress_monitor(self, commands):
        """使用进度监控面板执行命令"""
        # 创建进度监控窗口
        progress_window = tk.Toplevel(self.root)
        progress_window.title("BabelDOC 批量翻译进度监控")
        progress_window.geometry("800x600")
        progress_window.transient(self.root)

        # 主框架
        main_frame = ttk.Frame(progress_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 总体进度
        overall_frame = ttk.LabelFrame(main_frame, text="总体进度", padding="10")
        overall_frame.pack(fill=tk.X, pady=(0, 10))

        self.overall_progress = ttk.Progressbar(overall_frame, length=400, mode='determinate')
        self.overall_progress.pack(fill=tk.X, pady=5)

        self.overall_label = ttk.Label(overall_frame, text=f"准备执行 {len(commands)} 个任务...")
        self.overall_label.pack()

        # 任务列表
        tasks_frame = ttk.LabelFrame(main_frame, text="任务详情", padding="10")
        tasks_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 创建树形视图显示任务
        columns = ('文件名', '状态', '进度')
        self.task_tree = ttk.Treeview(tasks_frame, columns=columns, show='headings', height=15)

        # 设置列标题
        self.task_tree.heading('文件名', text='文件名')
        self.task_tree.heading('状态', text='状态')
        self.task_tree.heading('进度', text='进度')

        # 设置列宽
        self.task_tree.column('文件名', width=400)
        self.task_tree.column('状态', width=100)
        self.task_tree.column('进度', width=100)

        # 添加滚动条
        task_scrollbar = ttk.Scrollbar(tasks_frame, orient="vertical", command=self.task_tree.yview)
        self.task_tree.configure(yscrollcommand=task_scrollbar.set)

        self.task_tree.pack(side="left", fill="both", expand=True)
        task_scrollbar.pack(side="right", fill="y")

        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X)

        self.start_btn = ttk.Button(control_frame, text="开始执行", command=lambda: self.start_batch_execution(commands))
        self.start_btn.pack(side=tk.LEFT, padx=5)

        self.pause_btn = ttk.Button(control_frame, text="暂停", command=self.pause_execution, state=tk.DISABLED)
        self.pause_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(control_frame, text="停止", command=self.stop_execution, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="关闭", command=progress_window.destroy).pack(side=tk.RIGHT, padx=5)

        # 初始化任务列表
        self.task_processes = {}
        self.task_items = {}

        for i, command in enumerate(commands):
            # 从命令中提取文件名
            filename = self.extract_filename_from_command(command)
            item_id = self.task_tree.insert('', 'end', values=(filename, '等待中', '0%'))
            self.task_items[i] = item_id

        # 执行控制变量
        self.execution_paused = False
        self.execution_stopped = False

        # 存储进度窗口引用
        self.progress_window = progress_window

    def extract_filename_from_command(self, command):
        """从命令中提取文件名"""
        try:
            # 查找 --files 参数后的文件路径
            import re
            match = re.search(r'--files\s+"([^"]+)"', command)
            if match:
                filepath = match.group(1)
                return os.path.basename(filepath)
            return "未知文件"
        except:
            return "未知文件"

    def generate_batch_script(self, commands):
        """生成批处理脚本"""
        script_content = "@echo off\n"
        script_content += "chcp 65001 >nul\n"  # 设置UTF-8编码
        script_content += f"cd /d \"{os.getcwd()}\"\n"
        script_content += "echo BabelDOC 批量翻译脚本\n"
        script_content += "echo ========================\n"
        script_content += f"echo 总共 {len(commands)} 个翻译任务\n"
        script_content += "echo.\n\n"

        for i, command in enumerate(commands, 1):
            filename = self.extract_filename_from_command(command)
            script_content += f"echo 正在执行第 {i} 个任务: {filename}\n"
            script_content += f"echo 命令: {command}\n"
            script_content += f"{command}\n"
            script_content += "if errorlevel 1 (\n"
            script_content += f"    echo 第 {i} 个任务执行失败！\n"
            script_content += "    pause\n"
            script_content += ") else (\n"
            script_content += f"    echo 第 {i} 个任务执行成功！\n"
            script_content += ")\n"
            script_content += "echo.\n\n"

        script_content += "echo 所有任务执行完成！\n"
        script_content += "pause\n"

        # 保存脚本文件
        script_filename = filedialog.asksaveasfilename(
            title="保存批处理脚本",
            defaultextension=".bat",
            filetypes=[("批处理文件", "*.bat"), ("所有文件", "*.*")],
            initialname="babeldoc_batch.bat"
        )

        if script_filename:
            try:
                with open(script_filename, 'w', encoding='utf-8') as f:
                    f.write(script_content)

                result = messagebox.askyesno(
                    "脚本已生成",
                    f"批处理脚本已保存到:\n{script_filename}\n\n是否立即执行？"
                )

                if result:
                    subprocess.Popen(f'start "" "{script_filename}"', shell=True)

            except Exception as e:
                messagebox.showerror("错误", f"保存脚本失败: {e}")

    def start_batch_execution(self, commands):
        """开始批量执行"""
        print(f"开始批量执行，共 {len(commands)} 个命令")  # 调试信息

        self.start_btn.config(state=tk.DISABLED)
        self.pause_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.NORMAL)

        self.execution_paused = False
        self.execution_stopped = False

        # 在后台线程中执行
        threading.Thread(target=self.execute_tasks_sequentially, args=(commands,), daemon=True).start()
        print("后台线程已启动")  # 调试信息

    def execute_tasks_sequentially(self, commands):
        """按顺序执行任务"""
        print(f"execute_tasks_sequentially 开始执行，共 {len(commands)} 个命令")  # 调试信息
        completed = 0
        total = len(commands)

        for i, command in enumerate(commands):
            print(f"开始执行第 {i+1} 个任务: {command[:50]}...")  # 调试信息
            if self.execution_stopped:
                break

            # 等待暂停状态结束
            while self.execution_paused and not self.execution_stopped:
                import time
                time.sleep(0.1)

            if self.execution_stopped:
                break

            # 更新任务状态为执行中
            def update_to_running(task_idx):
                self.update_task_status(task_idx, "执行中", "0%")
            self.root.after(0, lambda: update_to_running(i))

            try:
                print(f"正在执行命令: {command}")  # 调试信息
                # 执行命令
                process = subprocess.Popen(
                    command,
                    shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    cwd=os.getcwd()
                )

                self.task_processes[i] = process
                print(f"进程已启动，PID: {process.pid}")  # 调试信息

                # 等待进程完成
                stdout, stderr = process.communicate()
                print(f"进程完成，返回码: {process.returncode}")  # 调试信息
                if stderr:
                    print(f"错误输出: {stderr}")  # 调试信息
                if stdout:
                    print(f"标准输出: {stdout[:200]}...")  # 调试信息（截取前200字符）

                if process.returncode == 0:
                    # 成功
                    print(f"任务 {i+1} 执行成功")  # 调试信息
                    def update_to_completed(task_idx):
                        self.update_task_status(task_idx, "完成", "100%")
                    self.root.after(0, lambda: update_to_completed(i))
                    completed += 1
                else:
                    # 失败
                    print(f"任务 {i+1} 执行失败，返回码: {process.returncode}")  # 调试信息
                    def update_to_failed(task_idx):
                        self.update_task_status(task_idx, "失败", "0%")
                    self.root.after(0, lambda: update_to_failed(i))

            except Exception as e:
                print(f"任务 {i+1} 执行异常: {e}")  # 调试信息
                def update_to_error(task_idx):
                    self.update_task_status(task_idx, "错误", "0%")
                self.root.after(0, lambda: update_to_error(i))

            # 更新总体进度
            progress = (i + 1) / total * 100
            def update_progress(prog, comp, tot):
                self.update_overall_progress(prog, comp, tot)
            self.root.after(0, lambda: update_progress(progress, completed, total))

        # 执行完成
        self.root.after(0, self.execution_completed)

    def update_task_status(self, task_index, status, progress):
        """更新任务状态"""
        if task_index in self.task_items:
            item_id = self.task_items[task_index]
            current_values = self.task_tree.item(item_id, 'values')
            new_values = (current_values[0], status, progress)
            self.task_tree.item(item_id, values=new_values)

    def update_overall_progress(self, progress, completed, total):
        """更新总体进度"""
        self.overall_progress['value'] = progress
        self.overall_label.config(text=f"已完成 {completed}/{total} 个任务 ({progress:.1f}%)")

    def execution_completed(self):
        """执行完成"""
        self.start_btn.config(state=tk.NORMAL)
        self.pause_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.DISABLED)
        messagebox.showinfo("完成", "所有翻译任务已执行完成！")

    def pause_execution(self):
        """暂停执行"""
        if self.execution_paused:
            self.execution_paused = False
            self.pause_btn.config(text="暂停")
        else:
            self.execution_paused = True
            self.pause_btn.config(text="继续")

    def stop_execution(self):
        """停止执行"""
        self.execution_stopped = True

        # 终止所有正在运行的进程
        for process in self.task_processes.values():
            try:
                process.terminate()
            except:
                pass

        self.start_btn.config(state=tk.NORMAL)
        self.pause_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.DISABLED)

        messagebox.showinfo("已停止", "批量执行已停止！")

    def parse_commands(self, command_text):
        """解析命令文本，提取所有有效命令"""
        commands = []
        lines = command_text.split('\n')

        for line in lines:
            line = line.strip()
            # 跳过注释行和空行
            if line and not line.startswith('#') and line.startswith('babeldoc'):
                commands.append(line)

        return commands

    def execute_commands_in_terminals(self, commands):
        """在多个终端中执行命令"""
        for i, command in enumerate(commands):
            try:
                # 使用 cmd 启动新的命令提示符窗口
                # /k 参数表示执行命令后保持窗口打开
                # /c 参数表示执行命令后关闭窗口
                cmd_command = f'start "BabelDOC 翻译任务 {i+1}" cmd /k "cd /d "{os.getcwd()}" && {command}"'

                subprocess.Popen(cmd_command, shell=True)

                # 稍微延迟一下，避免同时启动太多进程
                import time
                time.sleep(0.5)

            except Exception as e:
                print(f"启动第 {i+1} 个终端失败: {e}")
                # 继续执行其他命令，不因为一个失败而停止全部
    
    def clear_settings(self):
        """清空所有设置"""
        # 清空文件相关变量
        self.file_var.set("")
        self.selected_files = []
        self.file_configs = {}  # 清空个性化配置
        self.update_files_display()

        # 清空其他文本变量
        self.pages_var.set("")
        self.output_var.set("")
        self.openai_model_var.set(self.config.get('model', 'gpt-4o-mini'))
        self.openai_base_url_var.set(self.config.get('base_url', 'https://api.openai.com/v1'))
        self.openai_api_key_var.set(self.config.get('api', ''))
        self.max_pages_var.set("")
        self.qps_var.set("4")
        self.min_text_length_var.set("5")
        self.custom_prompt_var.set("")
        self.short_line_split_factor_var.set("0.8")
        
        # 重置布尔变量
        self.openai_var.set(True)
        self.enhance_compatibility_var.set(False)
        self.skip_clean_var.set(False)
        self.dual_translate_first_var.set(False)
        self.disable_rich_text_var.set(False)
        self.skip_scanned_detection_var.set(False)
        self.ocr_workaround_var.set(False)
        self.translate_table_text_var.set(False)
        self.no_dual_var.set(False)
        self.no_mono_var.set(False)
        self.ignore_cache_var.set(False)
        self.debug_var.set(False)
        self.split_short_lines_var.set(False)
        
        # 重置下拉框
        self.lang_in_var.set("en")
        self.lang_out_var.set("zh")
        self.watermark_var.set("watermarked")

def main():
    try:
        import pyperclip
    except ImportError:
        print("警告: 未安装 pyperclip 库，将使用系统剪贴板功能")
    
    root = tk.Tk()
    app = BabelDOCGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()